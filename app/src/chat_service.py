"""
Chat service module for handling conversations with the AI agent.
"""
import logging
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage
from .agent import EnrollmentAgent
from .config import SYSTEM_PROMPT

logger = logging.getLogger(__name__)


class ChatService:
    """Service class for managing chat interactions with the AI agent."""
    
    def __init__(self, agent: EnrollmentAgent):
        """
        Initialize the chat service with an agent.
        
        Args:
            agent: The EnrollmentAgent instance to use for processing messages
        """
        self.agent = agent
        self.session_threads = {}  # Store thread IDs for different sessions
    
    def get_or_create_thread_id(self, session_id: str = "default") -> str:
        """
        Get or create a thread ID for a session.
        
        Args:
            session_id: Unique identifier for the chat session
        
        Returns:
            Thread ID for the session
        """
        if session_id not in self.session_threads:
            self.session_threads[session_id] = f"thread_{session_id}_{len(self.session_threads)}"
        return self.session_threads[session_id]
    
    async def process_message(self, message: str, session_id: str = "default") -> Dict[str, Any]:
        """
        Process a user message and return the agent's response.
        
        Args:
            message: The user's message
            session_id: Session identifier for conversation context
        
        Returns:
            Dictionary containing the response and metadata
        """
        try:
            # Validate input
            if not message or not message.strip():
                return {
                    "response": "Please provide a message for me to respond to.",
                    "error": None,
                    "success": True
                }
            
            # Create human message
            human_message = HumanMessage(content=message.strip())
            thread_id = self.get_or_create_thread_id(session_id)
            
            logger.info(f"Processing message for session {session_id}: {message[:100]}...")
            
            # Process through agent
            result = self.agent.graph.invoke(
                {"messages": [human_message]},
                config={"configurable": {"thread_id": thread_id}}
            )
            
            # Extract response
            if result and "messages" in result and result["messages"]:
                final_message = result["messages"][-1]
                response_content = final_message.content if hasattr(final_message, 'content') else str(final_message)
            else:
                response_content = "I apologize, but I couldn't process your request. Please try rephrasing your question."
            
            logger.info(f"Successfully processed message for session {session_id}")
            
            return {
                "response": response_content,
                "error": None,
                "success": True,
                "session_id": session_id,
                "thread_id": thread_id
            }
            
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return {
                "response": "I'm sorry, I encountered an error while processing your request. Please try again or rephrase your question.",
                "error": error_msg,
                "success": False,
                "session_id": session_id
            }
    
    def clear_session(self, session_id: str) -> bool:
        """
        Clear a chat session.
        
        Args:
            session_id: Session identifier to clear
        
        Returns:
            True if session was cleared, False if it didn't exist
        """
        if session_id in self.session_threads:
            del self.session_threads[session_id]
            logger.info(f"Cleared session {session_id}")
            return True
        return False
    
    def get_active_sessions(self) -> list:
        """
        Get list of active session IDs.
        
        Returns:
            List of active session IDs
        """
        return list(self.session_threads.keys())
