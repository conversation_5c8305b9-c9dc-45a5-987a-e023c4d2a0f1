"""
Tools for enrollment and admission prediction.
This module contains various prediction tools for the EnrollmentAgent.
"""
import os
import pandas as pd
import numpy as np
import joblib
import logging
from langchain_core.tools import tool
from pathlib import Path
from typing import Dict, Any
from .config import (
    ENROLLMENT_MODEL_PATH, 
    SCALER_X_PATH, 
    SCALER_Y_PATH,
    ADMISSION_CNN_MODEL_PATH,
    ADMISSION_LSTM_MODEL_PATH,
    ADMISSION_SCALER_PATH,
    TARGET_SCALER_PATH,
    REGRESSOR_PATH
)

logger = logging.getLogger(__name__)

# Expected features for enrollment prediction model
EXPECTED_FEATURES = [
    'Year', 'Previous_Year_Enrollment', 'Applications_Received', 'Acceptance_Rate', 
    'Conversion_Rate', 'Secondary_School_Graduates', 'Scholarship_Funds_Available', 
    'Average_Entry_Test_Score', 'Female_Applicant_Percentage', 'Urban_Applicant_Percentage', 
    'International_Applicant_Percentage', 'Faculty_Student_Ratio'
]


@tool
def predict_enrollment(
    year: int,
    previous_year_enrollment: int,
    applications_received: int,
    acceptance_rate: float,
    conversion_rate: float,
    secondary_school_graduates: int,
    scholarship_funds_available: float,
    average_entry_test_score: float,
    female_applicant_percentage: float,
    urban_applicant_percentage: float,
    international_applicant_percentage: float,
    faculty_student_ratio: float
) -> str:
    """
    Predicts university enrollment for a given year based on various input factors.
    
    Args:
        year: The target year for prediction
        previous_year_enrollment: Actual enrollment from previous year
        applications_received: Total applications received
        acceptance_rate: Proportion of applicants offered admission (0.0-1.0)
        conversion_rate: Proportion of accepted students who enroll (0.0-1.0)
        secondary_school_graduates: Number of secondary school graduates
        scholarship_funds_available: Total scholarship funds available
        average_entry_test_score: Average standardized test score
        female_applicant_percentage: Percentage of female applicants (0-100)
        urban_applicant_percentage: Percentage of urban applicants (0-100)
        international_applicant_percentage: Percentage of international applicants (0-100)
        faculty_student_ratio: Faculty to student ratio
    
    Returns:
        Formatted string with prediction results
    """
    try:
        # Validate inputs
        if not (0.0 <= acceptance_rate <= 1.0):
            return "Error: Acceptance rate must be between 0.0 and 1.0"
        
        if not (0.0 <= conversion_rate <= 1.0):
            return "Error: Conversion rate must be between 0.0 and 1.0"
        
        if not (0 <= female_applicant_percentage <= 100):
            return "Error: Female applicant percentage must be between 0 and 100"
        
        if not (0 <= urban_applicant_percentage <= 100):
            return "Error: Urban applicant percentage must be between 0 and 100"
        
        if not (0 <= international_applicant_percentage <= 100):
            return "Error: International applicant percentage must be between 0 and 100"

        # Check if model files exist
        if not all(os.path.exists(p) for p in [ENROLLMENT_MODEL_PATH, SCALER_X_PATH, SCALER_Y_PATH]):
            missing_files = []
            if not os.path.exists(ENROLLMENT_MODEL_PATH):
                missing_files.append("enrollment model")
            if not os.path.exists(SCALER_X_PATH):
                missing_files.append("feature scaler")
            if not os.path.exists(SCALER_Y_PATH):
                missing_files.append("target scaler")
            
            return f"Error: Missing required model files: {', '.join(missing_files)}"

        # Import TensorFlow here to avoid import errors if not available
        try:
            import tensorflow as tf
        except ImportError:
            return "Error: TensorFlow is required but not installed. Please install TensorFlow to use enrollment prediction."

        # Load model and scalers
        logger.info("Loading enrollment prediction model and scalers...")
        model = tf.keras.models.load_model(str(ENROLLMENT_MODEL_PATH))
        scaler_x = joblib.load(str(SCALER_X_PATH))
        scaler_y = joblib.load(str(SCALER_Y_PATH))

        # Prepare input data
        input_data = {
            'Year': year,
            'Previous_Year_Enrollment': previous_year_enrollment,
            'Applications_Received': applications_received,
            'Acceptance_Rate': acceptance_rate,
            'Conversion_Rate': conversion_rate,
            'Secondary_School_Graduates': secondary_school_graduates,
            'Scholarship_Funds_Available': scholarship_funds_available,
            'Average_Entry_Test_Score': average_entry_test_score,
            'Female_Applicant_Percentage': female_applicant_percentage,
            'Urban_Applicant_Percentage': urban_applicant_percentage,
            'International_Applicant_Percentage': international_applicant_percentage,
            'Faculty_Student_Ratio': faculty_student_ratio
        }

        # Create input array in correct feature order
        input_array = np.array([[input_data[feat] for feat in EXPECTED_FEATURES]], dtype=np.float32)

        # Scale input features
        input_scaled = scaler_x.transform(input_array)

        # Make prediction
        pred_scaled = model.predict(input_scaled, verbose=0)

        # Inverse transform prediction
        prediction_original_scale = scaler_y.inverse_transform(pred_scaled)
        predicted_enrollment = int(round(prediction_original_scale[0][0]))

        # Calculate percentage change
        percentage_change = ((predicted_enrollment - previous_year_enrollment) / previous_year_enrollment) * 100

        # Format response
        result = f"""📊 **Enrollment Prediction Results**

**Year:** {year}
**Predicted Enrollment:** {predicted_enrollment:,} students
**Previous Year Enrollment:** {previous_year_enrollment:,} students
**Projected Change:** {percentage_change:+.1f}%

**Input Parameters:**
• Applications Received: {applications_received:,}
• Acceptance Rate: {acceptance_rate:.1%}
• Conversion Rate: {conversion_rate:.1%}
• Secondary School Graduates: {secondary_school_graduates:,}
• Scholarship Funds: ${scholarship_funds_available:,.2f}
• Average Test Score: {average_entry_test_score:.1f}
• Demographics: {female_applicant_percentage:.1f}% female, {urban_applicant_percentage:.1f}% urban, {international_applicant_percentage:.1f}% international
• Faculty-Student Ratio: 1:{1/faculty_student_ratio:.1f}

**Analysis:**
The model predicts {'an increase' if percentage_change > 0 else 'a decrease' if percentage_change < 0 else 'no change'} in enrollment for {year}. This prediction is based on historical enrollment patterns and the provided institutional and demographic factors."""

        logger.info(f"Successfully predicted enrollment: {predicted_enrollment}")
        return result

    except Exception as e:
        error_msg = f"An error occurred during enrollment prediction: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return f"Error: {error_msg}"


@tool
def predict_admission_chance(
    gre_score: float,
    toefl_score: float,
    sop: float,
    lor: float,
    cgpa: float
) -> str:
    """
    Predicts the chance of admission for a graduate program applicant.
    
    Args:
        gre_score: GRE score (130-170)
        toefl_score: TOEFL score (0-120)
        sop: Statement of Purpose score (1-5)
        lor: Letter of Recommendation score (1-5)
        cgpa: CGPA (0-10)
    
    Returns:
        Formatted string with admission chance prediction
    """
    try:
        # Validate inputs
        if not (130 <= gre_score <= 170):
            return "Error: GRE score must be between 130 and 170"
        
        if not (0 <= toefl_score <= 120):
            return "Error: TOEFL score must be between 0 and 120"
        
        if not (1 <= sop <= 5):
            return "Error: SOP score must be between 1 and 5"
        
        if not (1 <= lor <= 5):
            return "Error: LOR score must be between 1 and 5"
        
        if not (0 <= cgpa <= 10):
            return "Error: CGPA must be between 0 and 10"

        # Try to use the most reliable model available
        prediction_result = None
        model_used = None

        # Try CNN model first
        if os.path.exists(ADMISSION_CNN_MODEL_PATH) and os.path.exists(ADMISSION_SCALER_PATH):
            try:
                import tensorflow as tf
                model = tf.keras.models.load_model(str(ADMISSION_CNN_MODEL_PATH))
                scaler = joblib.load(str(ADMISSION_SCALER_PATH))
                
                # Prepare input data - note the space in 'LOR ' column name
                input_data = pd.DataFrame({
                    'GRE Score': [gre_score],
                    'TOEFL Score': [toefl_score],
                    'SOP': [sop],
                    'LOR ': [lor],  # Note the trailing space
                    'CGPA': [cgpa]
                })
                
                scaled_input = scaler.transform(input_data)
                prediction = model.predict(scaled_input, verbose=0)
                prediction_result = float(prediction[0][0])
                model_used = "CNN"
                
            except Exception as e:
                logger.warning(f"CNN model failed: {str(e)}")

        # Try regressor model if CNN failed
        if prediction_result is None and os.path.exists(REGRESSOR_PATH):
            try:
                model = joblib.load(str(REGRESSOR_PATH))
                
                input_data = pd.DataFrame({
                    'GRE Score': [gre_score],
                    'TOEFL Score': [toefl_score],
                    'SOP': [sop],
                    'LOR ': [lor],
                    'CGPA': [cgpa]
                })
                
                prediction_result = float(model.predict(input_data)[0])
                model_used = "Regressor"
                
            except Exception as e:
                logger.warning(f"Regressor model failed: {str(e)}")

        if prediction_result is None:
            return "Error: No admission prediction model is available or working"

        # Convert to percentage and format
        admission_percentage = prediction_result * 100
        
        # Determine admission likelihood category
        if admission_percentage >= 80:
            likelihood = "Very High"
            color = "🟢"
        elif admission_percentage >= 60:
            likelihood = "High"
            color = "🟡"
        elif admission_percentage >= 40:
            likelihood = "Moderate"
            color = "🟠"
        elif admission_percentage >= 20:
            likelihood = "Low"
            color = "🔴"
        else:
            likelihood = "Very Low"
            color = "⚫"

        result = f"""🎓 **Graduate Admission Prediction**

{color} **Admission Chance:** {admission_percentage:.1f}%
**Likelihood:** {likelihood}

**Applicant Profile:**
• GRE Score: {gre_score:.0f}/170
• TOEFL Score: {toefl_score:.0f}/120
• Statement of Purpose: {sop:.1f}/5
• Letter of Recommendation: {lor:.1f}/5
• CGPA: {cgpa:.2f}/10

**Model Used:** {model_used}

**Recommendation:**
{'Strong candidate - consider applying to reach schools' if admission_percentage >= 70 
else 'Good candidate - apply to a mix of reach and match schools' if admission_percentage >= 50
else 'Consider improving test scores or applying to more accessible programs' if admission_percentage >= 30
else 'Significant improvement needed in academic credentials'}"""

        logger.info(f"Successfully predicted admission chance: {admission_percentage:.1f}%")
        return result

    except Exception as e:
        error_msg = f"An error occurred during admission prediction: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return f"Error: {error_msg}"
