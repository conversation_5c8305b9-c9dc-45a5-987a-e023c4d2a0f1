"""
FastAPI application for EnrollPredict.ai
Main web application handling routes and API endpoints.
"""
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from langchain_groq import <PERSON>tGroq
from langgraph.checkpoint.memory import InMemorySaver

from .agent import EnrollmentAgent
from .chat_service import ChatService
from .prediction_tools import predict_enrollment, predict_admission_chance
from .config import (
    BASE_DIR, STATIC_DIR, TEMPLATES_DIR, 
    GROQ_API_KEY, DEFAULT_MODEL, DEFAULT_TEMPERATURE, SYSTEM_PROMPT,
    DEBUG, HOST, PORT
)

# Configure logging
logging.basicConfig(
    level=logging.INFO if not DEBUG else logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="EnrollPredict.ai",
    description="AI-powered enrollment and admission prediction system",
    version="1.0.0",
    debug=DEBUG
)

# Mount static files
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# Setup templates
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# Initialize memory for conversation persistence
memory = InMemorySaver()

# Initialize the AI model and agent
try:
    if not GROQ_API_KEY:
        logger.error("GROQ_API_KEY not found in environment variables")
        raise ValueError("GROQ_API_KEY is required")
    
    model = ChatGroq(
        temperature=DEFAULT_TEMPERATURE, 
        model=DEFAULT_MODEL,
        api_key=GROQ_API_KEY
    )
    
    tools = [predict_enrollment, predict_admission_chance]
    
    agent = EnrollmentAgent(
        model=model, 
        tools=tools, 
        memory=memory,
        system=SYSTEM_PROMPT
    )
    
    # Initialize chat service
    chat_service = ChatService(agent)
    
    logger.info("Application initialized successfully")
    
except Exception as e:
    logger.error(f"Failed to initialize application: {str(e)}")
    raise

@app.get("/")
async def root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/landing")
async def landing(request: Request):
    return templates.TemplateResponse("landing.html", {"request": request})

@app.get("/home")
async def home(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/about")
async def about_page(request: Request):
    return templates.TemplateResponse("about.html", {"request": request})

@app.get("/chat")
async def chat_page(request: Request):
    return templates.TemplateResponse("chat.html", {"request": request})

@app.post("/chat")
async def chat(request: Request):
    """Handle chat messages from users."""
    try:
        data = await request.json()
        message = data.get("message")
        
        if not message:
            return JSONResponse(
                content={"response": "Please provide a message.", "success": False},
                status_code=400
            )
        
        # Get session ID from request (could be from headers, cookies, etc.)
        session_id = data.get("session_id", "default")
        
        # Process message through chat service
        result = await chat_service.process_message(message, session_id)
        
        return JSONResponse(content={
            "response": result["response"],
            "success": result["success"],
            "session_id": result.get("session_id", session_id)
        })
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}", exc_info=True)
        return JSONResponse(
            content={
                "response": "I'm sorry, I encountered an error while processing your request. Please try again.",
                "success": False
            },
            status_code=500
        )

@app.get("/developers")
async def developers_page(request: Request):
    return templates.TemplateResponse("developers.html", {"request": request})




