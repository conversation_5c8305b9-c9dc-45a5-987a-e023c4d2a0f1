"""
Enrollment prediction agent using Lang<PERSON>raph for conversational AI.
"""
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import SystemMessage, AIMessage
from langgraph.graph import add_messages
from typing import Annotated, TypedDict, Literal, List
from langchain_groq import <PERSON><PERSON>G<PERSON>q
from langchain_community.tools import tool
from langchain_community.tools.tavily_search import TavilySearchResults
from dotenv import load_dotenv
from langgraph.prebuilt import ToolNode
from langgraph.types import Command
import logging
import pandas as pd

load_dotenv()
logger = logging.getLogger(__name__)


class State(TypedDict):
    """State definition for the agent conversation."""
    messages: Annotated[list, add_messages]


class EnrollmentAgent:
    """
    EnrollmentAgent handles conversational AI for enrollment prediction tasks.
    Uses LangGraph for managing conversation flow and tool usage.
    """
    
    def __init__(self, model: ChatGroq, tools: List, memory, system: str = ""):
        """
        Initialize the enrollment prediction agent.
        
        Args:
            model: ChatGroq model instance
            tools: List of tools available to the agent
            memory: Memory checkpoint for conversation persistence
            system: System prompt for the agent
        """
        self.system = system
        self.model = model.bind_tools(tools)
        self.checkpointer = memory
        self.tools_node = ToolNode(tools)
        
        # Build the conversation workflow
        workflow = StateGraph(State)
        workflow.add_node("llm", self.call_llm)
        workflow.add_node("tools", self.tools_node)
        
        workflow.add_edge(START, "llm")
        workflow.add_edge("tools", "llm")
        workflow.add_conditional_edges("llm", self.should_continue)
        
        self.graph = workflow.compile(checkpointer=self.checkpointer)
        logger.info("EnrollmentAgent initialized successfully")

    def should_continue(self, state: State) -> Literal["tools", "__end__"]:
        """
        Determine whether to continue with tools or end the conversation.
        
        Args:
            state: Current conversation state
            
        Returns:
            Next node to execute
        """
        messages = state.get("messages", [])
        if not messages:
            return "__end__"
            
        last_message = messages[-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "tools"
        return "__end__"

    def call_llm(self, state: State) -> Command[Literal["tools", "__end__"]]:
        """
        Call the language model with the current conversation state.
        
        Args:
            state: Current conversation state
            
        Returns:
            Command with the next action and updated state
        """
        try:
            messages = [SystemMessage(content=self.system)] + state["messages"]
            response = self.model.invoke(messages)
            
            if hasattr(response, 'tool_calls') and len(response.tool_calls) > 0:
                next_node = "tools"
            else:
                next_node = "__end__"
            
            return Command(goto=next_node, update={"messages": response})
            
        except Exception as e:
            logger.error(f"Error in call_llm: {str(e)}", exc_info=True)
            error_message = AIMessage(content="I apologize, but I encountered an error while processing your request. Please try again.")
            return Command(goto="__end__", update={"messages": error_message})


        
            
            
    

    

